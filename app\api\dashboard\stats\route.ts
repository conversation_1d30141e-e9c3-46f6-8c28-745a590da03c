import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Project from '@/models/Project';
import Lead from '@/models/Lead';
import Testimonial from '@/models/Testimonial';
import User from '@/models/User';
import { withAuth, withErrorHandling, withCors, AuthenticatedRequest } from '@/middleware/auth';

// GET /api/dashboard/stats - Get dashboard statistics (admin only)
async function getStatsHandler(req: AuthenticatedRequest) {
  await connectDB();

  try {
    // Get basic counts
    const [
      totalProjects,
      totalLeads,
      totalTestimonials,
      totalUsers,
      projectsByType,
      projectsByStatus,
      leadsByStatus,
      recentLeads,
      featuredProjects
    ] = await Promise.all([
      Project.countDocuments(),
      Lead.countDocuments(),
      Testimonial.countDocuments({ isApproved: true }),
      User.countDocuments({ isActive: true }),
      
      // Projects by type
      Project.aggregate([
        { $group: { _id: '$type', count: { $sum: 1 } } }
      ]),
      
      // Projects by status
      Project.aggregate([
        { $group: { _id: '$status', count: { $sum: 1 } } }
      ]),
      
      // Leads by status
      Lead.aggregate([
        { $group: { _id: '$status', count: { $sum: 1 } } }
      ]),
      
      // Recent leads (last 7 days)
      Lead.countDocuments({
        createdAt: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) }
      }),
      
      // Featured projects count
      Project.countDocuments({ featured: true })
    ]);

    // Get leads by month for the current year
    const currentYear = new Date().getFullYear();
    const leadsByMonth = await Lead.aggregate([
      {
        $match: {
          createdAt: {
            $gte: new Date(currentYear, 0, 1),
            $lt: new Date(currentYear + 1, 0, 1)
          }
        }
      },
      {
        $group: {
          _id: { $month: '$createdAt' },
          count: { $sum: 1 }
        }
      },
      {
        $sort: { '_id': 1 }
      }
    ]);

    // Format the data
    const stats = {
      overview: {
        totalProjects,
        totalLeads,
        totalTestimonials,
        totalUsers,
        recentLeads,
        featuredProjects
      },
      projects: {
        byType: projectsByType.reduce((acc: any, item: any) => {
          acc[item._id] = item.count;
          return acc;
        }, {}),
        byStatus: projectsByStatus.reduce((acc: any, item: any) => {
          acc[item._id] = item.count;
          return acc;
        }, {})
      },
      leads: {
        byStatus: leadsByStatus.reduce((acc: any, item: any) => {
          acc[item._id] = item.count;
          return acc;
        }, {}),
        byMonth: leadsByMonth.map((item: any) => ({
          month: item._id,
          count: item.count
        }))
      }
    };

    return NextResponse.json({
      success: true,
      data: stats
    });

  } catch (error) {
    console.error('Get dashboard stats error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch dashboard statistics' },
      { status: 500 }
    );
  }
}

// Route handlers
export const GET = withMiddleware(
  withCors,
  withAuth,
  withErrorHandling
)(getStatsHandler);

// Helper function to combine middlewares
function withMiddleware(...middlewares: Array<(handler: any) => any>) {
  return function(handler: (req: AuthenticatedRequest) => Promise<NextResponse>) {
    return middlewares.reduceRight((acc, middleware) => middleware(acc), handler);
  };
}
